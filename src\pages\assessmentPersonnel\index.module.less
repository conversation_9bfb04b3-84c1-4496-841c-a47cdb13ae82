/* 考核人员页面样式 */

/* ==================== 变量定义 ==================== */
@font-size-base: 12px;
@margin-base: 10px;
@margin-large: 20px;
@border-radius-base: 4px;

/* ==================== 混入定义 ==================== */

/* 统一字体大小设置 */
.font-size-mixin() {
  font-size: @font-size-base !important;
}

/* 按钮容器基础样式混入 */
.btn-container-base() {
  .ant-btn {
    .font-size-mixin();

    border-radius: @border-radius-base;

    /* 按钮悬停效果优化 */
    &:hover {
      transition: all 0.2s ease-in-out;
    }
  }
}

/* Flexbox 布局混入 */
.flex-end() {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

/* ==================== 主要样式 ==================== */

/* 考核人员页面容器 */
.assessment-personnel-container {
  width: 100%;
  height: 100%;
  font-size: @font-size-base;
  line-height: 1.5;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
	padding: 10px;

  :global {
    /* 统一设置 Ant Design 组件字体大小 */
    .ant-tabs,
    .ant-tabs-tab,
    .ant-tabs-tab-btn,
    .ant-tabs-content,
    .ant-table,
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td,
    .ant-btn,
    .ant-btn-sm,
    .ant-empty-description,
    .ant-pagination,
    .ant-pagination-item,
    .ant-select,
    .ant-input {
      .font-size-mixin();
    }

    /* 表格样式优化 */
    .ant-table {
      .ant-table-thead > tr > th {
        background-color: #fafafa;
        font-weight: 600;
      }
			
      .ant-table-tbody > tr > td {
        padding: 4px 8px;
      }

      .ant-table-tbody > tr:hover > td {
        background-color: #f5f5f5;
      }

      /* 斑马纹样式 */
      .table-row-striped {
        background-color: #f9f9f9;

        &:hover {
          background-color: #f0f0f0 !important;
        }
      }
    }

    /* 标签页样式优化 */
    .ant-tabs-tab {
      padding: 8px 16px;

      &.ant-tabs-tab-active {
        font-weight: 600;
      }
    }
  }
}

/* ==================== 组件样式 ==================== */

/* 用户选择容器 */
.pick-user-container {
  :global {
    /* 隐藏无边框输入框 */
    .ant-input-affix-wrapper-borderless {
      display: none;
    }
  }
}

/* 底部按钮容器 */
.btn-container {
  .btn-container-base();
  .flex-end();

  margin-top: @margin-large;
  gap: 8px; /* 按钮间距 */
}

/* 添加按钮容器 */
.add-btn-container {
  .btn-container-base();

  text-align: right;
  margin-bottom: @margin-base;
}
