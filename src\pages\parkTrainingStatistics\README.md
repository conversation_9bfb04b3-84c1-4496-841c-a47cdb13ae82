# 园区培训统计页面

## 功能描述

这个页面参考 MonitorDrawer 的实现方式，用于展示园区培训统计信息。页面分为两个部分：

1. **上半部分：基本信息表单** - 展示园区的基本培训统计信息
2. **下半部分：培训考试记录列表** - 展示详细的培训考试记录

## 使用方式

```tsx
import ParkTrainingStatistics from '@/pages/parkTrainingStatistics';

// 在父组件中使用
<ParkTrainingStatistics
  defaultQuery={{
    id: 'park-id-123' // 园区ID，用于查询数据
  }}
  onComplete={(data) => {
    console.log('完成回调:', data);
  }}
  closeModal={() => {
    console.log('关闭弹框');
  }}
/>
```

## Props 参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| defaultQuery | `{ id?: string }` | 是 | 默认查询参数，包含园区ID |
| onComplete | `(data: Record<string, string>[]) => void` | 否 | 完成回调函数 |
| closeModal | `() => void` | 否 | 关闭弹框方法 |

## 数据接口

### 基本信息接口 (BasicInfo)

```typescript
interface BasicInfo {
  id?: string;
  parkName?: string;           // 园区名称
  unitName?: string;           // 所属单位
  totalPersonnel?: number;     // 总人数
  trainedPersonnel?: number;   // 已培训人数
  passedPersonnel?: number;    // 考试通过人数
  trainingRate?: string;       // 培训完成率
  passRate?: string;           // 考试通过率
  createDate?: string;         // 创建时间
  updateDate?: string;         // 更新时间
}
```

### 培训考试记录接口 (TrainingRecord)

```typescript
interface TrainingRecord {
  id: string;
  peopleName: string;          // 姓名
  employeeId: string;          // 工号
  telephone: string;           // 联系电话
  learningObject: string;      // 学习对象
  ifFinishedStudy: string;     // 是否完成学习 (1:是, 0:否)
  learningDuration: string;    // 学习时长(分钟)
  ifFinishedExam: string;      // 是否完成考试 (1:是, 0:否)
  scores: string;              // 考试分数
  qualify: string;             // 是否合格 (1:合格, 0:不合格)
  retake: string;              // 是否补考 (1:是, 0:否)
  examDate?: string;           // 考试时间
  studyStartDate?: string;     // 学习开始时间
  studyEndDate?: string;       // 学习结束时间
}
```

## API 接口

页面使用以下API接口获取数据：

1. **查询基本信息**: `GET /gw/training-api/parkTraining/detail?id={parkId}`
2. **查询培训记录**: `GET /gw/training-api/parkTraining/records?parkId={parkId}`

## 特性

- ✅ 只读展示，不支持编辑
- ✅ 根据 defaultQuery 中的 id 自动查询数据
- ✅ 响应式设计，支持移动端
- ✅ 表格支持分页、排序、滚动
- ✅ 加载状态指示
- ✅ 错误处理

## 样式

页面使用 CSS Modules，样式文件位于 `index.module.less`。主要样式类：

- `.container` - 页面容器
- `.basicInfoCard` - 基本信息卡片
- `.recordsCard` - 记录列表卡片

## 注意事项

1. 页面需要传入有效的园区ID才能正常显示数据
2. 所有表单字段都是只读的，不支持编辑
3. 培训记录表格支持横向滚动，适配大量列数据
4. 页面会自动处理加载状态和错误情况
