---
title: 园区培训统计
order: 2
---

本 Demo 演示园区培训统计页面的基础用法。

```jsx
import React, { Component } from 'react';
import ReactDOM from 'react-dom';
import ParkTrainingStatistics from '@/pages/parkTrainingStatistics';

class App extends Component {
  render() {
    return (
      <div>
        <ParkTrainingStatistics
          defaultQuery={{
            id: 'park-001' // 园区ID
          }}
          onComplete={(data) => {
            console.log('完成回调:', data);
          }}
          closeModal={() => {
            console.log('关闭弹框');
          }}
        />
      </div>
    );
  }
}

ReactDOM.render(<App />, mountNode);
```
