.container {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.basicInfoCard {
  margin-bottom: 16px;

  .ant-card-body {
    padding: 24px;
  }
}

.recordsCard {
  .ant-card-body {
    padding: 24px;
  }

  .ant-table-wrapper {
    .ant-table {
      font-size: 12px;
    }

    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
      text-align: center;
    }

    .ant-table-tbody > tr > td {
      text-align: center;
    }

    .ant-table-tbody > tr:nth-child(even) {
      background-color: #fafafa;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #e6f7ff;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 8px;
  }

  .basicInfoCard,
  .recordsCard {
    .ant-card-body {
      padding: 16px;
    }
  }
}
