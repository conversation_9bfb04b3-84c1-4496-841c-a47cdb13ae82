import React, { useState, useEffect } from 'react';
import { YTHForm } from 'yth-ui';
import { Spin, Card, Divider, Table } from 'antd';
import trainingApi from '@/service/trainingApi';
import type { BasicInfo, TrainingRecord, ParkTrainingData } from '@/service/trainingApi';
import style from './index.module.less';

type PropsTypes = {
  /** 完成回调 */
  onComplete?: (data: Record<string, string>[]) => void;
  /** 关闭弹框方法 */
  closeModal?: () => void;
  /** 默认查询参数 */
  defaultQuery?: Record<string, unknown> & {
    id?: string;
  };
};

/**
 * @description 园区培训统计查看页面
 * @returns React.FC
 */
const ParkTrainingStatistics: React.FC<PropsTypes> = ({
  onComplete, // 完成回调
  closeModal, // 关闭弹框方法
  defaultQuery,
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [basicInfo, setBasicInfo] = useState<BasicInfo>({});
  const [trainingRecords, setTrainingRecords] = useState<TrainingRecord[]>([]);
  const form = YTHForm.useForm();

  // 查询园区培训统计数据
  const queryParkTrainingData = async () => {
    if (!defaultQuery?.id) return;

    try {
      setIsLoading(true);
      const data: ParkTrainingData | null = await trainingApi.queryParkTrainingData(defaultQuery.id as string);

      if (data) {
        setBasicInfo(data.basicInfo);
        setTrainingRecords(data.trainingRecords);

        // 设置表单数据
        form.setValues({
          parkName: data.basicInfo.parkName,
          planNumber: data.basicInfo.planNumber,
          planName: data.basicInfo.planName,
          trainingObject: data.basicInfo.trainingObject,
          learningPeriod: data.basicInfo.learningPeriod,
          examPeriod: data.basicInfo.examPeriod,
          totalPersonnel: data.basicInfo.totalPersonnel,
          studiedPersonnel: data.basicInfo.studiedPersonnel,
          studyRate: data.basicInfo.studyRate,
          examPersonnel: data.basicInfo.examPersonnel,
          passedPersonnel: data.basicInfo.passedPersonnel,
          passRate: data.basicInfo.passRate,
        });
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('查询园区培训统计数据失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (defaultQuery?.id) {
      queryParkTrainingData();
    }
  }, [defaultQuery?.id]);

  return (
    <div className={style.container}>
      <Spin spinning={isLoading}>
        {/* 基本信息表单 */}
        <Card title="基本信息" className={style.basicInfoCard}>
          <YTHForm form={form} col={3}>
            <YTHForm.Item
              name="planNumber"
              title="计划编号"
              labelType={1}
              componentName="Input"
              componentProps={{
                disabled: true,
                placeholder: '计划编号',
              }}
            />

            <YTHForm.Item
              name="planName"
              title="计划名称"
              labelType={1}
              componentName="Input"
              componentProps={{
                disabled: true,
                placeholder: '计划名称',
              }}
            />

            <YTHForm.Item
              name="trainingObject"
              title="培训对象"
              labelType={1}
              componentName="Input"
              componentProps={{
                disabled: true,
                placeholder: '培训对象',
              }}
            />

            <YTHForm.Item
              name="learningPeriod"
              title="学习时间"
              labelType={1}
              componentName="Input"
              componentProps={{
                disabled: true,
                placeholder: '学习时间',
              }}
            />

            <YTHForm.Item
              name="examPeriod"
              title="考试时间"
              labelType={1}
              componentName="Input"
              componentProps={{
                disabled: true,
                placeholder: '考试时间',
              }}
            />

            <YTHForm.Item
              name="totalPersonnel"
              title="总人数"
              labelType={1}
              componentName="InputNumber"
              componentProps={{
                disabled: true,
                placeholder: '总人数',
              }}
            />

            <YTHForm.Item
              name="studiedPersonnel"
              title="学习人数"
              labelType={1}
              componentName="InputNumber"
              componentProps={{
                disabled: true,
                placeholder: '学习人数',
              }}
            />

            <YTHForm.Item
              name="studyRate"
              title="学习完成率(%)"
              labelType={1}
              componentName="Input"
              componentProps={{
                disabled: true,
                placeholder: '学习完成率',
              }}
            />

            <YTHForm.Item
              name="examPersonnel"
              title="考试人数"
              labelType={1}
              componentName="InputNumber"
              componentProps={{
                disabled: true,
                placeholder: '考试人数',
              }}
            />

            <YTHForm.Item
              name="passedPersonnel"
              title="考试通过人数"
              labelType={1}
              componentName="InputNumber"
              componentProps={{
                disabled: true,
                placeholder: '考试通过人数',
              }}
            />

            <YTHForm.Item
              name="passRate"
              title="考试通过率(%)"
              labelType={1}
              componentName="Input"
              componentProps={{
                disabled: true,
                placeholder: '考试通过率',
              }}
            />
          </YTHForm>
        </Card>

        <Divider />

        {/* 培训考试记录列表 */}
        <Card title="培训考试记录" className={style.recordsCard}>
          <Table<TrainingRecord>
            dataSource={trainingRecords}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total: number) => `共 ${total} 条记录`,
            }}
            columns={[
              {
                title: '姓名',
                dataIndex: 'peopleName',
                key: 'peopleName',
                width: 120,
                fixed: 'left',
                align: 'center',
              },
              {
                title: '学习时长',
                dataIndex: 'studyHours',
                key: 'studyHours',
                width: 100,
                align: 'center',
              },
              {
                title: '考试成绩',
                dataIndex: 'examScore',
                key: 'examScore',
                width: 100,
                align: 'center',
              },
              {
                title: '学习状态',
                dataIndex: 'studyStatus',
                key: 'studyStatus',
                width: 120,
                align: 'center',
              },
              {
                title: '学习进度(%)',
                dataIndex: 'studyRate',
                key: 'studyRate',
                width: 120,
                align: 'center',
              },
              {
                title: '考试状态',
                dataIndex: 'examStatus',
                key: 'examStatus',
                width: 120,
                align: 'center',
              },
              {
                title: '考试结果',
                dataIndex: 'examResult',
                key: 'examResult',
                width: 100,
                align: 'center',
              },
              {
                title: '考试次数',
                dataIndex: 'examCount',
                key: 'examCount',
                width: 100,
                align: 'center',
              },
            ]}
            scroll={{ x: 800 }}
            size="small"
            bordered
          />
        </Card>
      </Spin>
    </div>
  );
};

export default ParkTrainingStatistics;
