import React from 'react';

type PropsTypes = {
  /** 完成回调 */
  onComplete?: (data: Record<string, string>[]) => void;
  /** 关闭弹框方法 */
  closeModal?: () => void;
  /** 默认查询参数 */
  defaultQuery?: Record<string, unknown>;
};

/**
 * @description 园区培训统计查看页面
 * @returns React.FC
 */
const ParkTrainingStatistics: React.FC<PropsTypes> = ({
  onComplete, // 完成回调
  closeModal, // 关闭弹框方法
  defaultQuery,
}) => {
  return <div>园区培训统计</div>;
};

export default ParkTrainingStatistics;
