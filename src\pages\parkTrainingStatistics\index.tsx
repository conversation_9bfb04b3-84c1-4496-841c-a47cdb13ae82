import React, { useState, useEffect } from 'react';
import { YTHForm } from 'yth-ui';
import { Spin, Card, Divider, Table } from 'antd';
import trainingApi from '@/service/trainingApi';
import type { BasicInfo, TrainingRecord } from '@/service/trainingApi';
import style from './index.module.less';

type PropsTypes = {
  /** 完成回调 */
  onComplete?: (data: Record<string, string>[]) => void;
  /** 关闭弹框方法 */
  closeModal?: () => void;
  /** 默认查询参数 */
  defaultQuery?: Record<string, unknown> & {
    id?: string;
  };
};

/**
 * @description 园区培训统计查看页面
 * @returns React.FC
 */
const ParkTrainingStatistics: React.FC<PropsTypes> = ({
  onComplete, // 完成回调
  closeModal, // 关闭弹框方法
  defaultQuery,
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [basicInfo, setBasicInfo] = useState<BasicInfo>({});
  const [trainingRecords, setTrainingRecords] = useState<TrainingRecord[]>([]);
  const form = YTHForm.useForm();

  // 查询基本信息
  const queryBasicInfo = async () => {
    if (!defaultQuery?.id) return;

    try {
      setIsLoading(true);
      const data = await trainingApi.queryParkTrainingDetail(defaultQuery.id as string);

      if (data) {
        setBasicInfo(data);

        // 设置表单数据
        form.setValues({
          parkName: data.parkName,
          unitName: data.unitName,
          totalPersonnel: data.totalPersonnel,
          trainedPersonnel: data.trainedPersonnel,
          passedPersonnel: data.passedPersonnel,
          trainingRate: data.trainingRate,
          passRate: data.passRate,
          createDate: data.createDate,
          updateDate: data.updateDate,
        });
      }
    } catch (error) {
      console.error('查询基本信息失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 查询培训考试记录
  const queryTrainingRecords = async () => {
    if (!defaultQuery?.id) return;

    try {
      const records = await trainingApi.queryParkTrainingRecords(defaultQuery.id as string);
      setTrainingRecords(records);
    } catch (error) {
      console.error('查询培训记录失败:', error);
    }
  };

  useEffect(() => {
    if (defaultQuery?.id) {
      queryBasicInfo();
      queryTrainingRecords();
    }
  }, [defaultQuery?.id]);

  return (
    <div className={style.container}>
      <Spin spinning={isLoading}>
        {/* 基本信息表单 */}
        <Card title="基本信息" className={style.basicInfoCard}>
          <YTHForm form={form} col={2}>
            <YTHForm.Item
              name="id"
              title="ID"
              display="hidden"
              componentName="Input"
            />

            <YTHForm.Item
              name="parkName"
              title="园区名称"
              labelType={1}
              componentName="Input"
              componentProps={{
                disabled: true,
                placeholder: '园区名称',
              }}
            />

            <YTHForm.Item
              name="unitName"
              title="所属单位"
              labelType={1}
              componentName="Input"
              componentProps={{
                disabled: true,
                placeholder: '所属单位',
              }}
            />

            <YTHForm.Item
              name="totalPersonnel"
              title="总人数"
              labelType={1}
              componentName="InputNumber"
              componentProps={{
                disabled: true,
                placeholder: '总人数',
              }}
            />

            <YTHForm.Item
              name="trainedPersonnel"
              title="已培训人数"
              labelType={1}
              componentName="InputNumber"
              componentProps={{
                disabled: true,
                placeholder: '已培训人数',
              }}
            />

            <YTHForm.Item
              name="passedPersonnel"
              title="考试通过人数"
              labelType={1}
              componentName="InputNumber"
              componentProps={{
                disabled: true,
                placeholder: '考试通过人数',
              }}
            />

            <YTHForm.Item
              name="trainingRate"
              title="培训完成率"
              labelType={1}
              componentName="Input"
              componentProps={{
                disabled: true,
                placeholder: '培训完成率',
                suffix: '%',
              }}
            />

            <YTHForm.Item
              name="passRate"
              title="考试通过率"
              labelType={1}
              componentName="Input"
              componentProps={{
                disabled: true,
                placeholder: '考试通过率',
                suffix: '%',
              }}
            />

            <YTHForm.Item
              name="createDate"
              title="创建时间"
              labelType={1}
              componentName="Input"
              componentProps={{
                disabled: true,
                placeholder: '创建时间',
              }}
            />

            <YTHForm.Item
              name="updateDate"
              title="更新时间"
              labelType={1}
              componentName="Input"
              componentProps={{
                disabled: true,
                placeholder: '更新时间',
              }}
            />
          </YTHForm>
        </Card>

        <Divider />

        {/* 培训考试记录列表 */}
        <Card title="培训考试记录" className={style.recordsCard}>
          <Table<TrainingRecord>
            dataSource={trainingRecords}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total: number) => `共 ${total} 条记录`,
            }}
            columns={[
              {
                title: '姓名',
                dataIndex: 'peopleName',
                key: 'peopleName',
                width: 100,
                fixed: 'left',
              },
              {
                title: '工号',
                dataIndex: 'employeeId',
                key: 'employeeId',
                width: 120,
              },
              {
                title: '联系电话',
                dataIndex: 'telephone',
                key: 'telephone',
                width: 130,
              },
              {
                title: '学习对象',
                dataIndex: 'learningObject',
                key: 'learningObject',
                width: 150,
              },
              {
                title: '是否完成学习',
                dataIndex: 'ifFinishedStudy',
                key: 'ifFinishedStudy',
                width: 120,
                render: (text: string) => {
                  return text === '1' ? '是' : '否';
                },
              },
              {
                title: '学习时长(分钟)',
                dataIndex: 'learningDuration',
                key: 'learningDuration',
                width: 130,
              },
              {
                title: '学习开始时间',
                dataIndex: 'studyStartDate',
                key: 'studyStartDate',
                width: 150,
              },
              {
                title: '学习结束时间',
                dataIndex: 'studyEndDate',
                key: 'studyEndDate',
                width: 150,
              },
              {
                title: '是否完成考试',
                dataIndex: 'ifFinishedExam',
                key: 'ifFinishedExam',
                width: 120,
                render: (text: string) => {
                  return text === '1' ? '是' : '否';
                },
              },
              {
                title: '考试分数',
                dataIndex: 'scores',
                key: 'scores',
                width: 100,
              },
              {
                title: '是否合格',
                dataIndex: 'qualify',
                key: 'qualify',
                width: 100,
                render: (text: string) => {
                  return text === '1' ? '合格' : '不合格';
                },
              },
              {
                title: '是否补考',
                dataIndex: 'retake',
                key: 'retake',
                width: 100,
                render: (text: string) => {
                  return text === '1' ? '是' : '否';
                },
              },
              {
                title: '考试时间',
                dataIndex: 'examDate',
                key: 'examDate',
                width: 150,
              },
            ]}
            scroll={{ x: 1500 }}
            size="small"
            bordered
          />
        </Card>
      </Spin>
    </div>
  );
};

export default ParkTrainingStatistics;
