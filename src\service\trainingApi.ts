import { trainRequest } from '@/request';
import type { ApiResponse } from '@/service/baseApi';

// 基本信息接口
export interface BasicInfo {
  id?: string;
  parkName?: string; // 园区名称
  planNumber?: string; // 计划编号
  planName?: string; // 计划名称
  trainingObject?: string; // 培训对象
  learningPeriod?: string; // 学习时间
  examPeriod?: string; // 考试时间
  totalPersonnel?: number; // 总人数
  studiedPersonnel?: number; // 学习人数
  studyRate?: string; // 学习完成率(%)
  examPersonnel?: number; // 考试人数
  passedPersonnel?: number; // 考试通过人数
  passRate?: string; // 考试通过率(%)
}

// 培训考试记录接口
export interface TrainingRecord {
  id: string;
  peopleName: string; // 姓名
  studyHours: number; // 学习时长
  examScore: number; // 考试成绩
  studyStatus: string; // 学习状态
  studyRate: string; // 学习进度(%)
  examStatus: string; // 考试状态
  examResult: string; // 考试结果
  examCount: number; // 考试次数
}

// 完整的园区培训统计数据
export interface ParkTrainingData {
  basicInfo: BasicInfo;
  trainingRecords: TrainingRecord[];
}

/**
 * 培训相关API服务
 */
export default {
  /**
   * 查询园区培训统计完整数据（包含基本信息和培训记录）
   * @param id - 园区ID
   * @returns Promise<ParkTrainingData | null>
   */
  queryParkTrainingData: async (id: string): Promise<ParkTrainingData | null> => {
    try {
      const response: ApiResponse<ParkTrainingData> = await trainRequest.get('/parkTraining/statistics', {
        params: { id },
      });

      if (response.code === 200) {
        return response.data;
      }
      return null;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('查询园区培训统计数据失败:', error);
      return null;
    }
  },

  /**
   * 导出培训记录
   * @param params - 导出参数
   * @returns Promise<Blob | null>
   */
  exportTrainingRecords: async (params: Record<string, unknown>): Promise<Blob | null> => {
    try {
      const response = await trainRequest.post('/training/export', {
        data: params,
        responseType: 'blob',
      });

      return response as unknown as Blob;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('导出培训记录失败:', error);
      return null;
    }
  },
};
