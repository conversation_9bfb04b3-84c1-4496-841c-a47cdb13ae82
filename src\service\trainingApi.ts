import { trainRequest } from '@/request';
import type { ApiResponse } from '@/service/baseApi';

// 基本信息接口
export interface BasicInfo {
  id?: string;
  parkName?: string;
  unitName?: string;
  totalPersonnel?: number;
  trainedPersonnel?: number;
  passedPersonnel?: number;
  trainingRate?: string;
  passRate?: string;
  createDate?: string;
  updateDate?: string;
}

// 培训考试记录接口
export interface TrainingRecord {
  id: string;
  peopleName: string;
  employeeId: string;
  telephone: string;
  learningObject: string;
  ifFinishedStudy: string;
  learningDuration: string;
  ifFinishedExam: string;
  scores: string;
  qualify: string;
  retake: string;
  examDate?: string;
  studyStartDate?: string;
  studyEndDate?: string;
}

/**
 * 培训相关API服务
 */
export default {
  /**
   * 查询园区培训统计基本信息
   * @param id - 园区ID
   * @returns Promise<BasicInfo | null>
   */
  queryParkTrainingDetail: async (id: string): Promise<BasicInfo | null> => {
    try {
      const response: ApiResponse<BasicInfo> = await trainRequest.get('/parkTraining/detail', {
        params: { id },
      });
      
      if (response.code === 200) {
        return response.data;
      }
      return null;
    } catch (error) {
      console.error('查询园区培训统计基本信息失败:', error);
      return null;
    }
  },

  /**
   * 查询园区培训考试记录
   * @param parkId - 园区ID
   * @returns Promise<TrainingRecord[]>
   */
  queryParkTrainingRecords: async (parkId: string): Promise<TrainingRecord[]> => {
    try {
      const response: ApiResponse<{ list: TrainingRecord[] }> = await trainRequest.get('/parkTraining/records', {
        params: { parkId },
      });
      
      if (response.code === 200) {
        return response.data.list || [];
      }
      return [];
    } catch (error) {
      console.error('查询园区培训考试记录失败:', error);
      return [];
    }
  },

  /**
   * 查询培训统计汇总信息
   * @param params - 查询参数
   * @returns Promise<any>
   */
  queryTrainingSummary: async (params: Record<string, unknown>): Promise<unknown> => {
    try {
      const response: ApiResponse<unknown> = await trainRequest.get('/training/summary', {
        params,
      });
      
      if (response.code === 200) {
        return response.data;
      }
      return null;
    } catch (error) {
      console.error('查询培训统计汇总信息失败:', error);
      return null;
    }
  },

  /**
   * 导出培训记录
   * @param params - 导出参数
   * @returns Promise<Blob | null>
   */
  exportTrainingRecords: async (params: Record<string, unknown>): Promise<Blob | null> => {
    try {
      const response = await trainRequest.post('/training/export', {
        data: params,
        responseType: 'blob',
      });
      
      return response as unknown as Blob;
    } catch (error) {
      console.error('导出培训记录失败:', error);
      return null;
    }
  },
};
